<?php
require_once 'vendor/autoload.php';

// 載入 Laravel 應用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    // 查詢 id=908 的訂單詳細資料
    $order = DB::connection('main_db')->table('orderform')->where('id', 908)->first();

    if (!$order) {
        echo "Order 908 not found!\n";
        exit;
    }

    echo "=== Order 908 Details ===\n";
    foreach ($order as $key => $value) {
        echo "$key: " . ($value ?? 'NULL') . "\n";
    }

    // 模擬完整的 get_orders 查詢邏輯
    echo "\n=== Simulating Full get_orders Query ===\n";

    // 模擬請求參數 (全部選項)
    $state = 'New'; // 預設狀態
    $order_ship_status = '-1'; // 全部 (非垃圾桶)
    $distributor_id = ''; // 全部供應商
    $searchKey = ''; // 無搜尋關鍵字
    $receipts_state = ''; // 全部付款狀態
    $payment = ''; // 全部付款方式
    $transport = ''; // 全部物流方式
    $stock_status = ''; // 全部庫存狀態
    $do_award_time = ''; // 全部回饋狀態

    // 建構 WHERE 條件 (模擬 OrderHelper::get_orders)
    if ($state == 'New') {
        if ($order_ship_status == '-1') { // 全部(非垃圾桶)
            $where = "( order.status NOT IN ('Cancel', 'Return') )";
        } else {
            $where = "( order.status='New' )";
        }
    } else if ($state == 'Complete') {
        $where = "( order.status='Complete' )";
    } else if ($state == 'Trash') {
        $where = "( (order.status='Cancel') OR (order.status='Return') )";
    } else {
        $where = "( order.status='" . $state . "' )";
    }

    echo "Base WHERE condition: $where\n";

    // 測試基本條件
    $count = DB::connection('main_db')->table('orderform as order')
        ->where('order.id', 908)
        ->whereRaw($where)
        ->count();
    echo "Matches base condition: " . ($count > 0 ? 'YES' : 'NO') . "\n";

    // 加入其他過濾條件
    $additional_conditions = [];

    if ($receipts_state !== '') {
        $additional_conditions[] = "order.receipts_state='" . $receipts_state . "'";
    }

    if ($payment !== '') {
        $additional_conditions[] = "order.payment='" . $payment . "'";
    }

    if ($transport !== '') {
        $additional_conditions[] = "order.transport='" . $transport . "'";
    }

    if ($stock_status !== '') {
        $additional_conditions[] = "order.stock_status='" . $stock_status . "'";
    }

    if ($do_award_time !== '') {
        if ($do_award_time == '0') {
            $additional_conditions[] = "order.do_award_time=''";
        } else if ($do_award_time == '-1') {
            $additional_conditions[] = "order.do_award_time!=''";
        }
    }

    $full_where = $where;
    foreach ($additional_conditions as $condition) {
        $full_where .= " AND " . $condition;
        echo "Added condition: $condition\n";
    }

    echo "Full WHERE condition: $full_where\n";

    // 測試完整條件
    $query = DB::connection('main_db')->table('orderform as order')
        ->select('order.*', 'account.name', 'account.number')
        ->leftJoin('account', 'account.id', '=', 'order.user_id')
        ->whereRaw($full_where)
        ->where('order.id', 908);

    // 加入供應商過濾 (模擬 admin 類型)
    $admin_type = 'admin'; // 假設是管理員
    $my_distributor_id = 0;
    $distributor_id_where = '';

    if ($admin_type == 'admin') {
        if ($distributor_id !== '' && $distributor_id !== '-1') {
            $distributor_id_where = 'distributor_id="' . $distributor_id . '"';
        }
    }

    if ($distributor_id_where) {
        $query = $query->whereRaw($distributor_id_where);
        echo "Added distributor filter: $distributor_id_where\n";
    } else {
        echo "No distributor filter applied\n";
    }

    // 加入搜尋條件
    $searchKey = trim($searchKey);
    $query = $query->whereRaw("(
                          order.order_number LIKE '%$searchKey%' OR
                          order.transport_location_name LIKE '%$searchKey%' OR
                          order.transport_location_phone LIKE '%$searchKey%'
                        )");

    echo "Added search filter for: '$searchKey'\n";

    // 執行查詢
    $result = $query->first();
    echo "\nFinal result: " . ($result ? 'FOUND' : 'NOT FOUND') . "\n";

    if ($result) {
        echo "Order details from query:\n";
        echo "ID: " . $result->id . "\n";
        echo "Status: " . $result->status . "\n";
        echo "User: " . $result->name . " (" . $result->number . ")\n";
    }

    // 檢查是否有其他可能的過濾條件
    echo "\n=== Additional Checks ===\n";

    // 檢查 show_date 欄位
    echo "show_date: " . $order->show_date . "\n";
    echo "create_time: " . $order->create_time . " (timestamp: " . date('Y-m-d H:i:s', $order->create_time) . ")\n";

    // 檢查是否有時間相關的過濾
    echo "\n=== Time-based Filtering Checks ===\n";

    // 檢查 create_time = 0 的問題
    if ($order->create_time == 0) {
        echo "WARNING: create_time is 0 (1970-01-01), this might cause filtering issues!\n";

        // 檢查是否有基於 create_time 的隱藏過濾
        $orders_with_valid_time = DB::connection('main_db')->table('orderform as order')
            ->leftJoin('account', 'account.id', '=', 'order.user_id')
            ->whereRaw("( order.status NOT IN ('Cancel', 'Return') )")
            ->where('order.create_time', '>', 0)
            ->count();
        echo "Orders with create_time > 0: $orders_with_valid_time\n";

        $orders_with_zero_time = DB::connection('main_db')->table('orderform as order')
            ->leftJoin('account', 'account.id', '=', 'order.user_id')
            ->whereRaw("( order.status NOT IN ('Cancel', 'Return') )")
            ->where('order.create_time', '=', 0)
            ->count();
        echo "Orders with create_time = 0: $orders_with_zero_time\n";
    }

    // 檢查排序是否影響顯示
    echo "\n=== Sorting and Pagination Checks ===\n";
    $order_sql = 'order.create_time desc, id desc';

    $recent_orders = DB::connection('main_db')->table('orderform as order')
        ->select('order.id', 'order.create_time', 'order.status', 'order.order_number')
        ->leftJoin('account', 'account.id', '=', 'order.user_id')
        ->whereRaw("( order.status NOT IN ('Cancel', 'Return') )")
        ->whereRaw("(
                      order.order_number LIKE '%%' OR
                      order.transport_location_name LIKE '%%' OR
                      order.transport_location_phone LIKE '%%'
                    )")
        ->orderByRaw($order_sql)
        ->limit(10)
        ->get();

    echo "Top 10 orders by default sorting:\n";
    foreach ($recent_orders as $ord) {
        $time_str = $ord->create_time > 0 ? date('Y-m-d H:i:s', $ord->create_time) : 'Invalid (0)';
        echo "ID: {$ord->id}, Time: {$time_str}, Status: {$ord->status}, Number: {$ord->order_number}\n";
    }

    // 檢查訂單 908 在排序中的位置
    $position_query = DB::connection('main_db')->table('orderform as order')
        ->select(DB::raw('COUNT(*) + 1 as position'))
        ->leftJoin('account', 'account.id', '=', 'order.user_id')
        ->whereRaw("( order.status NOT IN ('Cancel', 'Return') )")
        ->whereRaw("(
                      order.order_number LIKE '%%' OR
                      order.transport_location_name LIKE '%%' OR
                      order.transport_location_phone LIKE '%%'
                    )")
        ->whereRaw("(order.create_time > {$order->create_time} OR (order.create_time = {$order->create_time} AND order.id > 908))")
        ->first();

    echo "\nOrder 908 position in sorted list: " . $position_query->position . "\n";

    // 檢查分頁設定
    $per_page = 20; // 預設每頁顯示數量
    $page_number = ceil($position_query->position / $per_page);
    echo "Order 908 would appear on page: $page_number\n";

    // 建議修正方案
    echo "\n=== Suggested Fix ===\n";
    echo "Problem: Order 908 has create_time = 0, causing it to appear on page $page_number\n";
    echo "Solutions:\n";
    echo "1. Search for order number 'B19700101G9MLAQO2' or '908' in the search box\n";
    echo "2. Navigate to page $page_number to find the order\n";
    echo "3. Fix the create_time field to a proper timestamp\n";

    // 提供修正 create_time 的建議
    $suggested_time = strtotime('2024-01-01 00:00:00'); // 建議設定為合理的時間
    echo "4. Suggested SQL to fix create_time:\n";
    echo "   UPDATE orderform SET create_time = $suggested_time WHERE id = 908;\n";
    echo "   (This sets create_time to " . date('Y-m-d H:i:s', $suggested_time) . ")\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
